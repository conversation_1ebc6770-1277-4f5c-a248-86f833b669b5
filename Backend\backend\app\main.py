from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import asyncio
import numpy as np
import soundfile as sf

# For Google Cloud APIs
from google.cloud import speech, translate_v2 as translate, texttospeech

app = FastAPI(title="TalkSync Backend")

# Google Cloud clients
speech_client = speech.SpeechClient()
translate_client = translate.Client()
tts_client = texttospeech.TextToSpeechClient()

# Connected clients for broadcasting
connected_clients = []

# REST endpoint example
class TranslateRequest(BaseModel):
    text: str
    target_language: str  # 'hi' or 'pa'

@app.post("/translate")
async def translate_text(req: TranslateRequest):
    result = translate_client.translate(req.text, target_language=req.target_language)
    return {"translated_text": result["translatedText"]}

# WebSocket endpoint for audio streaming
@app.websocket("/ws/audio")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    connected_clients.append(websocket)
    try:
        while True:
            audio_bytes = await websocket.receive_bytes()
            
            # Convert bytes to float32 PCM array
            audio_array = np.frombuffer(audio_bytes, dtype=np.int16).astype(np.float32) / 32768.0

            # Here you can process audio: STT, Translation, TTS
            # For demo, we'll just echo length
            msg = f"Received {len(audio_array)} samples"
            
            # Broadcast to all connected clients
            for client in connected_clients:
                if client.client_state.value == 1:  # WebSocketState.CONNECTED
                    await client.send_text(msg)

    except WebSocketDisconnect:
        connected_clients.remove(websocket)
        print("Client disconnected")

# Simple HTML test page
@app.get("/")
async def get():
    html_content = """
    <html>
        <head><title>TalkSync Test</title></head>
        <body>
            <h1>TalkSync WebSocket Test</h1>
            <p>Open console to see messages.</p>
            <script>
                let ws = new WebSocket("ws://localhost:8000/ws/audio");
                ws.onmessage = (event) => { console.log("WS Msg:", event.data); };
            </script>
        </body>
    </html>
    """
    return HTMLResponse(content=html_content)
